import 'dart:async';
import 'dart:math' as math;

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:fiaranow_flutter/fcm.dart';
import 'package:fiaranow_flutter/l10n/app_localizations.dart';
import 'package:fiaranow_flutter/models/MobileUser.dart';
import 'package:fiaranow_flutter/states/AuthState.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_google_places_sdk/flutter_google_places_sdk.dart' as places_sdk;
import 'package:geoflutterfire_plus/geoflutterfire_plus.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:logging/logging.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

import '../config/tenant_config.dart';
import '../models/Trip.dart';
import '../models/TripStatus.dart';
import '../services/TripStateService.dart';
import 'ForegroundServiceHandler.dart';
import 'NavigationState/BaseNavigationState.dart';
import 'NavigationState/PreparationMixin.dart';

class NavigationState extends BaseNavigationState with PreparationMixin {
  final Logger _logger = Logger('Navigation');

  // -----------------
  // Initialization State
  // -----------------
  var isMapReady = false.obs;
  var isUserReady = false.obs;
  var isLocationReady = false.obs;

  // -----------------
  // Tab Controller
  // -----------------
  PageController? pageController;

  // -----------------
  // Map Position State
  // -----------------
  var currentMapPosition = Rxn<LatLng>();

  // -----------------
  // Location Tracking State
  // -----------------
  StreamSubscription<Position>? _positionStream;
  Timer? _positionUpdateDebounceTimer;
  var isFollowingPosition = false.obs;
  var isFollowingDriverPosition = false.obs;
  String _lastGeohash = '';
  int _locationStreamRetryCount = 0;
  static const int _maxLocationStreamRetries = 3;
  Timer? _locationStreamRetryTimer;
  StreamSubscription<DocumentSnapshot<MobileUser>>? _driverPositionSub;
  var driverLatestPosition = Rxn<LatLng>();

  // -----------------
  // Driver Trip State
  // -----------------
  StreamSubscription<QuerySnapshot<Trip>>? _driverTripRequestsSub;
  var driverTripRequests = RxList<Trip>([]);
  var selectedRequestTripId = RxnString();
  
  // Route data cache for trips without embedded route data
  final Map<String, RouteData> _routeDataCache = {};
  final Map<String, bool> _routeDataLoading = {};

  // -----------------
  // Passenger Trip State
  // -----------------
  StreamSubscription<QuerySnapshot<Trip>>? _passengerTripsSub;

  // Trip being viewed on map (separate from active trip)
  var viewingTrip = Rxn<Trip>();

  // -----------------
  // Lifecycle Methods
  // -----------------
  @override
  void onInit() {
    _logger.info('Initializing NavigationState');
    super.onInit();

    // - Watch for currentMobileUser to be available and set initial driving mode
    // - Watch also if the user type changes
    UserType? lastUserType;
    ever(authState.currentMobileUser, (MobileUser? user) {
      if (user != null) {
        isUserReady.value = true;
        _checkFullInitialization();

        final userType = user.primaryUserType;
        if (userType != null && lastUserType != userType) {
          _logger.info('User type changed from $lastUserType to $userType');
          lastUserType = userType;
          setDrivingMode(userType);
        }
      }
    });

    // Check location permission on init
    checkLocationPermission().then((_) {
      isLocationReady.value = true;
      _checkFullInitialization();
    });

    TripStatus? lastDriverTripStatus;
    final w1 = ever(driverTripRequests, (List<Trip> trips) {
      if (drivingMode.value == UserType.driver) {
        // --------------
        // DRIVER mode
        // --------------
        final trip = driverTripRequests.firstWhereOrNull((trip) => trip.status != TripStatus.preparing);

        // Auto-select first trip if none selected
        if (trips.isNotEmpty && selectedRequestTripId.value == null) {
          selectedRequestTripId.value = trips.first.id;
        } else if (trips.isEmpty) {
          selectedRequestTripId.value = null;
        }

        if (trip != null) {
          if (trip.status != lastDriverTripStatus) {
            // New status

            // - Driver is approaching
            if (trip.status == TripStatus.driverApproaching || trip.status == TripStatus.driverAwaiting) {
              // NOTE: Cost updates are now handled by the server-side state machine
              // The cost will be calculated and updated when the trip is completed

              // Enable wakelock when driver is approaching or awaiting passenger
              _enableWakeLock();
              // Start persistent notification
              ForegroundServiceHandler.startTripService(Get.context!);
            }

            // - Starting a trip
            if (trip.status == TripStatus.inProgress) {
              isNavigating.value = true;
              isFollowingPosition.value = true;

              // Enable wakelock to prevent screen from turning off during a trip
              _enableWakeLock();
              // Start persistent notification
              ForegroundServiceHandler.startTripService(Get.context!);
            }

            // - Trip ended
            if (trip.status == TripStatus.completed || trip.status == TripStatus.cancelled || trip.status == TripStatus.paid) {
              isNavigating.value = false;

              // Disable wakelock when the trip ends
              _disableWakeLock();
              // Stop persistent notification
              ForegroundServiceHandler.stopTripService();
            }
          }

          lastDriverTripStatus = trip.status;
        } else {
          if (lastDriverTripStatus != null) {
            _logger.info('Trip has been deleted: $lastDriverTripStatus (last status)');

            // Reset the state
            reset();
            final position = currentPosition.value;
            if (position != null && mapController != null) {
              mapController?.animateCamera(
                CameraUpdate.newCameraPosition(
                  CameraPosition(
                    target: position,
                    zoom: 16.0,
                  ),
                ),
              );
            }

            // Ensure wakelock is disabled when trip is deleted
            _disableWakeLock();
            // Stop persistent notification
            ForegroundServiceHandler.stopTripService();
          }

          lastDriverTripStatus = null;
        }
      }
    });

    TripStatus? lastRiderTripStatus;
    final w2 = ever(currentRiderTrip, (Trip? trip) {
      if (drivingMode.value == UserType.rider) {
        // --------------
        // RIDER mode
        // --------------
        if (trip != null) {
          // Handle status changes
          if (lastRiderTripStatus != trip.status) {
            // Stop following driver position when trip ends (completed, paid, or cancelled)
            if (trip.status == TripStatus.completed || trip.status == TripStatus.paid || trip.status == TripStatus.cancelled) {
              stopFollowingDriverLocation();
            }

            // Start following driver position when trip has a driver assigned
            if (trip.uidChosenDriver != null &&
                (trip.status == TripStatus.driverApproaching ||
                    trip.status == TripStatus.driverAwaiting ||
                    trip.status == TripStatus.inProgress)) {
              // Start the position listener if not already active
              if (_driverPositionSub == null) {
                _startDriverPositionListener();
              }
            }
          }

          lastRiderTripStatus = trip.status;
        } else {
          // Trip deleted - stop following driver and clean up
          stopFollowingDriverLocation();
          reset(stopCurrentTripSub: true);
          lastRiderTripStatus = null;
        }
      }
    });

    addToPostCleanupFunc(() {
      w1();
      w2();
    });
  }

  @override
  void onClose() {
    _logger.info('Closing NavigationState - stopping all subscriptions during app close/logout');
    stopLocationUpdates();
    _positionUpdateDebounceTimer?.cancel();
    _stopDriverPositionListener();
    stopListeningNearbyDrivers();
    _stopListeningToPassengerTrips();

    // When closing, reset state but don't call Get.delete<NavigationState>()
    reset(stopCurrentTripSub: true, stopDriverTripRequestsSub: true, stopNearbyDriversSub: true);

    // Make sure to disable wakelock when the app is closed
    _disableWakeLock();
    // Stop persistent notification
    ForegroundServiceHandler.stopTripService();

    super.onClose();
  }

  // -----------------
  // Mode Management Methods
  // -----------------
  void setDrivingMode(UserType userType) {
    // Can't change driving mode while navigating
    if (isNavigating.value) return;

    drivingMode.value = userType;

    clearStartAddress();
    clearDestinationAddress();

    if (userType == UserType.rider) {
      // Update tenant state instead of main document
      const tenantId = TenantConfig.TENANT_ID;
      mobileUsersColl.doc(authState.uid).collection('tenant_states').doc(tenantId).update({'isServiceActive': false});
      _stopListeningToDriverTripRequests();

      // Only start passenger listener if fully initialized
      if (isFullyInitialized.value) {
        _startListeningToPassengerTrips();
      }
    } else {
      // Switching to driver mode - stop following driver position if active
      stopFollowingDriverLocation();

      // For driver mode, start subscribing to trip requests
      _stopListeningToPassengerTrips();

      // Only start driver listener if fully initialized
      if (isFullyInitialized.value) {
        _startToSubscribeToDriverTripRequests();
      }

      // Check if there's an active trip that requires wakelock
      // This is a fallback check in case the subscription doesn't trigger the logic
      Future.delayed(const Duration(seconds: 1), () {
        final activeTrip = liveTrip;
        if (activeTrip != null) {
          final status = activeTrip.status;

          if (status == TripStatus.inProgress) {
            // Enable wakelock for the trip
            _enableWakeLock();
          } else if (status == TripStatus.driverApproaching || status == TripStatus.driverAwaiting) {
            // Enable wakelock for approaching/awaiting trips as well
            _enableWakeLock();
          }
        }
      });
    }

    FirebaseAnalytics.instance.logEvent(name: 'set_driving_mode', parameters: {'mode': userType.name});
  }

  Trip? get liveTrip {
    if (drivingMode.value == UserType.rider) {
      final trip = currentRiderTrip.value;
      // Don't show trip if it's dismissed by passenger
      if (trip?.passengerDismissed == true) {
        return null;
      }
      return (trip?.status == TripStatus.preparing || trip?.status == TripStatus.requestingDriver) ? null : trip;
    }
    return driverTripRequests
        .firstWhereOrNull((trip) => trip.status != TripStatus.preparing && trip.status != TripStatus.requestingDriver);
  }

  // Get the trip to display on map (active trip takes precedence over viewing trip)
  Trip? get displayTrip {
    final active = liveTrip;
    if (active != null) {
      return active;
    }
    return viewingTrip.value;
  }

  // Check if a trip can be shown on map (false if there's an active trip)
  bool get canShowOtherTrips {
    return liveTrip == null;
  }

  // -----------------
  // Driver Trip Request Methods
  // -----------------
  void _startToSubscribeToDriverTripRequests() {
    final uid = authState.uid;
    if (uid.isEmpty) {
      _logger.severe('No uid found');
      return;
    }

    _logger.info('✅ Starting to subscribe to driver trip requests for uid: $uid');

    final tripRequestsRef = tripsColl.where('uidChosenDriver', isEqualTo: uid).where('driverDismissed', isEqualTo: false);

    // Cancel any existing subscription before starting a new one
    _driverTripRequestsSub?.cancel();

    _driverTripRequestsSub = tripRequestsRef.snapshots().listen(
      (snapshot) async {
        final newList = snapshot.docs.map((doc) => doc.data()).toList();
        _logger.fine('Received ${newList.length} driver trip requests: ${newList.map((trip) => trip.id).join(', ')}');

        if (newList.isEmpty) {
          // Only now we know it's really empty
          cancelNotification();
        }
        
        // Preload route data for trips without embedded data
        for (final trip in newList) {
          if (trip.routeData == null && trip.routeDataIds?.main != null) {
            // This will cache the route data
            await getCachedRouteData(trip);
          }
        }

        // Check for any active trips on initial load
        // Note that driverTripRequests can be empty at any time, not only on initial load. It simply means that there are
        // currently no Trip requests for the current driver.
        if (driverTripRequests.isEmpty) {
          // First check for in-progress trips that need the wakelock enabled
          final inProgressTrip = newList.firstWhereOrNull((trip) => trip.status == TripStatus.inProgress);
          if (inProgressTrip != null) {
            // Enable wakelock for the in-progress trip
            _enableWakeLock();
          }
          // Also check for approaching or awaiting trips that need wakelock
          else {
            final approachingOrAwaitingTrip = newList.firstWhereOrNull(
                (trip) => trip.status == TripStatus.driverApproaching || trip.status == TripStatus.driverAwaiting);

            if (approachingOrAwaitingTrip != null) {
              // Enable wakelock for approaching/awaiting trips as well
              _enableWakeLock();
            }
          }
        }

        driverTripRequests.value = newList;
      },
      onError: (error) {
        if (authState.uid.isEmpty) {
          _logger.warning('No uid found, skipping driver trip requests listener');
          return;
        }

        _logger.severe('Error listening to driver trip requests', error, StackTrace.current);
        FirebaseAnalytics.instance.logEvent(name: 'driver_trip_requests_error', parameters: {'error': error.toString()});
        Get.snackbar(
          AppLocalizations.of(Get.context!)!.navigationState_error, // "Error"
          AppLocalizations.of(Get.context!)!
              .navigationState_driverTripRequestsError, // "Error listening to driver trip requests. Please restart the app."
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      },
    );
  }

  void _stopListeningToDriverTripRequests() {
    _logger.info('ℹ Stopping driver trip request listener - this should only happen during mode switch to passenger or logout');
    _driverTripRequestsSub?.cancel();
    _driverTripRequestsSub = null;
    driverTripRequests.clear();

    // Stop any ongoing Notifications (probably still ringing)
    cancelNotification();
  }

  // -----------------
  // Passenger Trip Listener Methods
  // -----------------
  void _startListeningToPassengerTrips() {
    final uid = authState.uid;
    if (uid.isEmpty) {
      _logger.severe('No uid found for passenger trips');
      return;
    }

    _logger.info('✅ Starting to listen for passenger trips for uid: $uid');

    // Query for trips where the current user is the passenger
    // IMPORTANT: Split into two queries to work with security rules
    // 1. Active trips (ongoing)
    // 2. Recent completed/paid trips that haven't been dismissed

    // Query for trips where the current user is the passenger
    // Include completed/paid trips so they remain visible until dismissed
    final passengerTripsQuery = tripsColl
        .where('uidPassenger', isEqualTo: uid)
        .where('passengerDismissed', isEqualTo: false)
        .orderBy('createdAt', descending: true)
        .limit(10);

    // Cancel any existing subscription before starting a new one
    _passengerTripsSub?.cancel();

    // Cancel any existing subscription before starting a new one
    _passengerTripsSub?.cancel();

    _passengerTripsSub = passengerTripsQuery.snapshots().listen(
      (snapshot) {
        _logger.info('Received ${snapshot.docs.length} passenger trips');

        if (snapshot.docs.isNotEmpty) {
          // Get all trips and prioritize active over completed/paid
          final trips = snapshot.docs.map((doc) => doc.data()).toList();

          // Filter out reserved trips - they should not appear until admin assigns a driver
          // Note: We must use local filtering because Firestore doesn't support != with other conditions
          final nonReservedTrips = trips.where((trip) => trip.status != TripStatus.reserved).toList();

          if (nonReservedTrips.isEmpty) {
            // No active trips found - clear current trip if any
            if (currentRiderTrip.value != null) {
              _logger.info('No active trips found for passenger, clearing current trip');
              clearCurrentRiderTrip();
            }
            return;
          }

          // Find the most relevant trip:
          // 1. First priority: Active trips (not completed/paid)
          // 2. Second priority: Most recent completed/paid trip
          final activeTrip =
              nonReservedTrips.firstWhereOrNull((trip) => trip.status != TripStatus.completed && trip.status != TripStatus.paid);

          final selectedTrip = activeTrip ?? nonReservedTrips.first;

          _logger.info('Selected trip: ${selectedTrip.id} with status: ${selectedTrip.status}');

          // Update current trip if different
          if (currentRiderTrip.value?.id != selectedTrip.id) {
            _logger.info('New trip detected for passenger, restoring state');
            // Clear any viewing trip when active trip is detected
            if (viewingTrip.value != null) {
              _logger.info('Clearing viewing trip due to active trip detection');
              clearViewingTrip();
            }
            restoreStateFromTrip(UserType.rider, selectedTrip);
          }
        } else {
          // No trips found - clear current trip if any
          if (currentRiderTrip.value != null) {
            _logger.info('No trips found for passenger, clearing current trip');
            clearCurrentRiderTrip();
          }
        }
      },
      onError: (error) {
        _logger.severe('Error listening to passenger trips', error);
      },
    );
  }

  void _stopListeningToPassengerTrips() {
    _logger.info('ℹ Stopping passenger trip listener');
    _passengerTripsSub?.cancel();
    _passengerTripsSub = null;
  }

  // -----------------
  // Map Controller Ready Handler
  // -----------------
  @override
  void onMapControllerReady() {
    _logger.info('Map controller is ready');
    isMapReady.value = true;
    _checkFullInitialization();

    // If we're already fully initialized and have a current trip, animate to it
    if (isFullyInitialized.value && drivingMode.value == UserType.rider && currentRiderTrip.value != null) {
      final trip = currentRiderTrip.value!;
      _logger.info('Animating camera to current rider trip: ${trip.id}');
      _animateCameraToTrip(trip);
    }
  }

  void _animateCameraToTrip(Trip trip) async {
    if (mapController == null) return;

    // Load route data if needed
    RouteData? routeData = await getCachedRouteData(trip);

    // Rerun the camera animation logic
    if (trip.startLocation != null && trip.arrivalLocation != null && routeData != null) {
      // Frame the route
      final bounds = routeData.bounds;
      mapController!.animateCamera(CameraUpdate.newLatLngBounds(bounds, 50));
    } else if (trip.startLocation != null && trip.arrivalLocation != null) {
      // Frame both locations
      final bounds = LatLngBounds(
        southwest: LatLng(
          math.min(trip.startLocation!.lat, trip.arrivalLocation!.lat) - 0.01,
          math.min(trip.startLocation!.lon, trip.arrivalLocation!.lon) - 0.01,
        ),
        northeast: LatLng(
          math.max(trip.startLocation!.lat, trip.arrivalLocation!.lat) + 0.01,
          math.max(trip.startLocation!.lon, trip.arrivalLocation!.lon) + 0.01,
        ),
      );
      mapController!.animateCamera(CameraUpdate.newLatLngBounds(bounds, 50));
    } else if (trip.startLocation != null) {
      // Center on start location
      mapController!.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(
            target: LatLng(trip.startLocation!.lat, trip.startLocation!.lon),
            zoom: 16.0,
          ),
        ),
      );
    }
  }

  void _checkFullInitialization() {
    if (isMapReady.value && isUserReady.value && isLocationReady.value && !isFullyInitialized.value) {
      _logger.info('All services ready, marking app as fully initialized');
      isFullyInitialized.value = true;
      _onFullyInitialized();
    }
  }

  void _onFullyInitialized() {
    _logger.info('App fully initialized, starting listeners based on current mode');

    // Start the appropriate listeners based on the current driving mode
    if (drivingMode.value == UserType.rider) {
      _startListeningToPassengerTrips();
    } else if (drivingMode.value == UserType.driver) {
      _startToSubscribeToDriverTripRequests();
    }

    // Process any pending actions
    processPendingActions();
  }

  // -----------------
  // Location Permission Methods
  // -----------------
  Future<void> checkLocationPermission() async {
    if (locationFullyFunctional.value) return;

    bool serviceEnabled;
    LocationPermission permission;

    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      await Geolocator.openLocationSettings();
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied || permission == LocationPermission.deniedForever) {
      permission = await Geolocator.requestPermission();
    }

    if (permission == LocationPermission.always || permission == LocationPermission.whileInUse) {
      locationFullyFunctional.value = true;
      startLocationUpdates();
    } else {
      locationFullyFunctional.value = false;
    }
  }

  // -----------------
  // Location Update Methods
  // -----------------
  Future<void> getCurrentLocation() async {
    Position position = await Geolocator.getCurrentPosition();
    recordCurrentLocation(LatLng(position.latitude, position.longitude));
  }

  void startLocationUpdates() {
    // Cancel any existing stream and timer before starting a new one
    stopLocationUpdates();

    try {
      _logger.info('Starting location updates stream');
      _positionStream = Geolocator.getPositionStream(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.bestForNavigation,
          distanceFilter: 4,
        ),
      ).listen(
        (Position position) {
          // Reset retry count on successful position update
          _locationStreamRetryCount = 0;

          recordCurrentLocation(LatLng(position.latitude, position.longitude));

          // Follow position if enabled
          if (isFollowingPosition.value == true && mapController != null) {
            mapController!.animateCamera(
              CameraUpdate.newCameraPosition(
                CameraPosition(
                  target: LatLng(position.latitude, position.longitude),
                  zoom: zoomLevel.value,
                ),
              ),
            );
          }
        },
        onError: (error) {
          _logger.severe('Error in location stream', error, StackTrace.current);
          FirebaseAnalytics.instance.logEvent(name: 'location_stream_error', parameters: {'error': error.toString()});

          // Attempt to restart the stream
          _handleLocationStreamError();
        },
        onDone: () {
          _logger.warning('Location stream closed unexpectedly');
          // If the stream completes unexpectedly, try to restart it
          _handleLocationStreamError();
        },
        cancelOnError: false, // Don't cancel on error, let our handler deal with it
      );
    } catch (e) {
      _logger.severe('Failed to start location stream', e, StackTrace.current);
      FirebaseAnalytics.instance.logEvent(name: 'location_stream_start_error', parameters: {'error': e.toString()});

      // Attempt to restart after error
      _handleLocationStreamError();
    }
  }

  void stopLocationUpdates() {
    _logger.info('Stopping location updates');
    _positionStream?.cancel();
    _positionStream = null;
    _locationStreamRetryTimer?.cancel();
    _locationStreamRetryTimer = null;
    _locationStreamRetryCount = 0;
  }

  void _handleLocationStreamError() {
    // Cancel any existing retry timer
    _locationStreamRetryTimer?.cancel();

    // Clean up the existing stream
    _positionStream?.cancel();
    _positionStream = null;

    // Increment retry count
    _locationStreamRetryCount++;

    if (_locationStreamRetryCount <= _maxLocationStreamRetries) {
      // Exponential backoff: 2^retry_count seconds (2, 4, 8 seconds)
      final retryDelaySeconds = (1 << (_locationStreamRetryCount - 1)) * 2;
      _logger.info(
          'Will retry location stream in $retryDelaySeconds seconds (attempt $_locationStreamRetryCount of $_maxLocationStreamRetries)');

      _locationStreamRetryTimer = Timer(Duration(seconds: retryDelaySeconds), () {
        _logger.info('Retrying location stream (attempt $_locationStreamRetryCount of $_maxLocationStreamRetries)');
        startLocationUpdates();
      });
    } else {
      _logger.severe('Maximum location stream retry attempts reached ($_maxLocationStreamRetries)');
      FirebaseAnalytics.instance.logEvent(name: 'location_stream_retry_exhausted');

      // Reset retry count after max attempts
      _locationStreamRetryCount = 0;

      // Notify user of the issue
      Get.snackbar(
        AppLocalizations.of(Get.context!)!.navigationState_locationError, // "Location Error"
        AppLocalizations.of(Get.context!)!
            .navigationState_locationTrackingError, // "Unable to track your location. Please check your location settings and restart the app."
        backgroundColor: Colors.red,
        colorText: Colors.white,
        duration: const Duration(seconds: 10),
      );
    }
  }

  void recordCurrentLocation(LatLng position) {
    currentPosition.value = position;
    _debounceUpdateCurrentPositionInFirebase(position);
  }

  // -----------------
  // Map Control Methods
  // -----------------
  void setFollowingPosition(bool following) {
    isFollowingPosition.value = following;

    if (following) {
      final position = currentPosition.value;
      if (position != null && mapController != null) {
        mapController!.animateCamera(
          CameraUpdate.newCameraPosition(
            CameraPosition(
              target: position,
              zoom: zoomLevel.value,
            ),
          ),
        );
      }
    }
  }

  Future<void> moveCameraToPosition(LatLng position, {double? zoomLevel}) async {
    if (mapController != null) {
      await mapController!.animateCamera(CameraUpdate.newCameraPosition(CameraPosition(
        target: position,
        zoom: zoomLevel ?? await mapController?.getZoomLevel() ?? 15,
      )));
    }
  }

  void moveCameraToCurrentPosition() {
    final position = currentPosition.value;
    if (position != null) {
      moveCameraToPosition(position);
    }
  }

  // -----------------
  // Place Methods
  // -----------------
  Future<Map<String, dynamic>?> getPlaceDetails(String placeId) async {
    final placeDetails = await placesSdk.fetchPlace(
      placeId,
      fields: [places_sdk.PlaceField.Name, places_sdk.PlaceField.Location],
    );
    if (placeDetails.place == null) return null;
    final location = placeDetails.place!.latLng!;

    return {
      'name': placeDetails.place!.name,
      'lat': location.lat,
      'lon': location.lng,
    };
  }

  // -----------------
  // Firebase Update Methods
  // -----------------
  void _debounceUpdateCurrentPositionInFirebase(LatLng position) {
    if (_positionUpdateDebounceTimer?.isActive ?? false) _positionUpdateDebounceTimer!.cancel();
    _positionUpdateDebounceTimer = Timer(const Duration(milliseconds: 500), () {
      _updateCurrentPositionInFirebase(position);
    });
  }

  Future<void> _updateCurrentPositionInFirebase(LatLng position) async {
    final uid = Get.find<AuthState>().uid;
    final geo = GeoFirePoint(GeoPoint(position.latitude, position.longitude));
    final geohash = geo.geohash;

    // Update Firestore document so that it can be queried by geohash
    if (_lastGeohash != geohash) {
      FirebaseFirestore.instance.collection('mobile_users').doc(uid).update({
        'position': geo.data,
        'lat': position.latitude,
        'lon': position.longitude,
      });
      _lastGeohash = geohash;
    }

    // DRIVER: if a Trip is live:
    // - Record a sub-collection 'logs' of the driver's position and timestamp
    // - Update the cost* fields of the Trip
    final trip = liveTrip;
    if (drivingMode.value == UserType.driver && trip != null) {
      if (trip.status == TripStatus.driverApproaching || trip.status == TripStatus.driverAwaiting) {
        // Position tracking now handled via logs subcollection
        trip.recordTripLog(position);
      } else if (trip.status == TripStatus.inProgress) {
        // Position tracking now handled via logs subcollection only
        trip.recordTripLog(position);
      }
    }
  }

  // -----------------
  // Driver Selection Methods
  // -----------------
  Future<void> selectDriver(MobileUser driver) async {
    if (currentRiderTrip.value == null) return;

    // Use TripStateService to request driver through the state machine
    await TripStateService.instance.requestDriver(
      tripId: currentRiderTrip.value!.id,
      driverUid: driver.uid,
      driverLocation: {
        'lat': driver.lat!,
        'lon': driver.lon!,
      },
    );
    FirebaseAnalytics.instance.logEvent(name: 'select_driver', parameters: {'driver_uid': driver.uid});
  }

  // -----------------
  // Navigation Control Methods
  // -----------------
  void startNavigation() {
    isNavigating.value = true;
    FirebaseAnalytics.instance.logEvent(name: 'start_navigation');
  }

  void stopNavigation() {
    isNavigating.value = false;
    FirebaseAnalytics.instance.logEvent(name: 'stop_navigation');
  }

  // -----------------
  // Trip Viewing Methods
  // -----------------
  void showTripOnMap(Trip trip) {
    // Don't allow showing other trips if there's an active trip
    if (!canShowOtherTrips) {
      _logger.warning('Cannot show trip ${trip.id} - active trip exists');
      Get.snackbar(
        AppLocalizations.of(Get.context!)!.navigationState_error,
        AppLocalizations.of(Get.context!)!
            .navigationState_cannotShowTripError, // "Cannot show trip while you have an active trip"
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      return;
    }

    _logger.info('Showing trip ${trip.id} on map');
    viewingTrip.value = trip;

    // Restore only the visual state (routes, positions) without setting as current trip
    _restoreViewingTripVisualState(trip);
  }

  // Restore visual state for on-demand viewing without setting as current trip
  void _restoreViewingTripVisualState(Trip trip) {
    // Set start position if available
    if (trip.startLocation != null) {
      startPosition.value = LatLng(trip.startLocation!.lat, trip.startLocation!.lon);
    }

    // Set destination position if available
    if (trip.arrivalLocation != null) {
      destinationPosition.value = LatLng(trip.arrivalLocation!.lat, trip.arrivalLocation!.lon);
    }

    // Don't set passenger count or other state that would affect active trips

    // Animate camera to show the trip route
    executeWhenReady(() {
      if (mapController == null) {
        _logger.warning('MapController still null after initialization');
        return;
      }

      // Add delay to allow bottom section to appear first
      Future.delayed(const Duration(milliseconds: 500), () {
        // Handle different location scenarios for camera positioning
        if (trip.startLocation != null && trip.arrivalLocation != null) {
          // Load route data for bounds
          getCachedRouteData(trip).then((routeData) {
            if (routeData != null) {
              // Scenario 1: Both locations and route data available - frame the route
              final bounds = routeData.bounds;
              mapController!.animateCamera(CameraUpdate.newLatLngBounds(bounds, 50));
            } else {
              // Scenario 2: Both locations but no route data - frame both with padding
              final bounds = LatLngBounds(
                southwest: LatLng(
                  math.min(trip.startLocation!.lat, trip.arrivalLocation!.lat) - 0.01,
                  math.min(trip.startLocation!.lon, trip.arrivalLocation!.lon) - 0.01,
                ),
                northeast: LatLng(
                  math.max(trip.startLocation!.lat, trip.arrivalLocation!.lat) + 0.01,
                  math.max(trip.startLocation!.lon, trip.arrivalLocation!.lon) + 0.01,
                ),
              );
              mapController!.animateCamera(CameraUpdate.newLatLngBounds(bounds, 50));
            }
          });
        } else if (trip.startLocation != null && trip.arrivalLocation != null) {
          // Scenario 2: Both locations but no route data - frame both with padding
          final bounds = LatLngBounds(
            southwest: LatLng(
              math.min(trip.startLocation!.lat, trip.arrivalLocation!.lat) - 0.01,
              math.min(trip.startLocation!.lon, trip.arrivalLocation!.lon) - 0.01,
            ),
            northeast: LatLng(
              math.max(trip.startLocation!.lat, trip.arrivalLocation!.lat) + 0.01,
              math.max(trip.startLocation!.lon, trip.arrivalLocation!.lon) + 0.01,
            ),
          );
          mapController!.animateCamera(CameraUpdate.newLatLngBounds(bounds, 50));
        } else if (trip.startLocation != null) {
          // Scenario 3: Only start location - center to that with zoom level 16
          mapController!.animateCamera(
            CameraUpdate.newCameraPosition(
              CameraPosition(
                target: LatLng(trip.startLocation!.lat, trip.startLocation!.lon),
                zoom: 16.0,
              ),
            ),
          );
        }
      });
    });

    // Don't set routeChosen or currentRiderTrip - this is just for viewing
  }

  void clearViewingTrip() {
    if (viewingTrip.value != null) {
      _logger.info('Clearing viewing trip ${viewingTrip.value?.id}');
      viewingTrip.value = null;

      // Reset the map state if no active trip
      if (liveTrip == null) {
        reset(stopCurrentTripSub: true, stopNearbyDriversSub: true);

        // Center map on current position
        final position = currentPosition.value;
        if (position != null && mapController != null) {
          mapController?.animateCamera(
            CameraUpdate.newCameraPosition(
              CameraPosition(
                target: position,
                zoom: 16.0,
              ),
            ),
          );
        }
      }
    }
  }

  // -----------------
  // Driver Location Following Methods
  // -----------------
  @override
  void startFollowingDriverLocation() {
    _logger.info('🚗 startFollowingDriverLocation called');

    // Stop following own position if it was active
    setFollowingPosition(false);

    // Start following driver position
    isFollowingDriverPosition.value = true;

    // Start listening to driver position from mobile_users
    _startDriverPositionListener();
  }

  void stopFollowingDriverLocation() {
    _logger.info('🛑 stopFollowingDriverLocation called');

    // Stop following driver position
    isFollowingDriverPosition.value = false;

    // Stop listening to driver position
    _stopDriverPositionListener();
  }

  void _startDriverPositionListener() {
    // Cancel any existing subscription
    _stopDriverPositionListener();

    final trip = currentRiderTrip.value;
    if (trip == null || trip.uidChosenDriver == null) {
      _logger
          .warning('Cannot start driver position listener: no trip or driver - trip: $trip, driverUid: ${trip?.uidChosenDriver}');
      return;
    }

    _logger.info('🚗 Starting driver position listener for driver ${trip.uidChosenDriver}');

    // Subscribe to the driver's position from mobile_users document
    _driverPositionSub = FirebaseFirestore.instance
        .collection('mobile_users')
        .doc(trip.uidChosenDriver)
        .withConverter<MobileUser>(
          fromFirestore: (snapshot, _) => MobileUser.fromFirestore(snapshot),
          toFirestore: (user, _) => user.toFirestore(),
        )
        .snapshots()
        .listen((snapshot) {
      if (snapshot.exists && snapshot.data() != null) {
        final driver = snapshot.data()!;
        if (driver.lat != null && driver.lon != null) {
          final newPosition = LatLng(driver.lat!, driver.lon!);

          // Update driver position
          driverLatestPosition.value = newPosition;

          _logger.fine('📍 Updated driver position: ${driver.lat}, ${driver.lon}');

          // Update camera if following is enabled
          if (isFollowingDriverPosition.value && mapController != null) {
            _logger.fine('📍 Animating camera to driver position');
            mapController!.animateCamera(
              CameraUpdate.newCameraPosition(
                CameraPosition(
                  target: newPosition,
                  zoom: zoomLevel.value,
                ),
              ),
            );
          } else if (isFollowingDriverPosition.value && mapController == null) {
            _logger.warning('📍 Cannot animate camera - mapController is null');
          }
        } else {
          _logger.warning('📍 Driver document exists but lat/lon are null');
        }
      } else {
        _logger.warning('📍 Driver document does not exist or data is null');
      }
    }, onError: (error) {
      _logger.severe('Error listening to driver position', error);
    });

    _logger.info('🚗 Driver position listener subscription created');
  }

  void _stopDriverPositionListener() {
    if (_driverPositionSub != null) {
      _logger.info('🛑 Stopping driver position listener');
      _driverPositionSub!.cancel();
      _driverPositionSub = null;
      driverLatestPosition.value = null;
    }
  }

  // -----------------
  // State Clear Methods
  // -----------------
  @override
  void reset({
    bool stopNearbyDriversSub = false,
    bool stopCurrentTripSub = false,
    bool stopDriverTripRequestsSub = false,
  }) {
    _logger.info(
        'Resetting NavigationState: stopNearbyDriversSub: $stopNearbyDriversSub, stopCurrentTripSub: $stopCurrentTripSub, stopDriverTripRequestsSub: $stopDriverTripRequestsSub');

    // Address and location selection state
    startPosition.value = null;
    destinationPosition.value = null;
    startAddress.value = null;
    destinationAddress.value = null;
    startAddressController.clear();
    destinationAddressController.clear();
    startAddressPredictions.clear();
    destinationAddressPredictions.clear();
    isStartAddressFocused.value = false;
    isDestinationAddressFocused.value = false;
    isChoosingStartPosition.value = true;
    isChoosingDestinationPosition.value = false;

    // Navigation state
    isNavigating.value = false;
    isFollowingPosition.value = false;
    isFollowingDriverPosition.value = false;
    _stopDriverPositionListener();
    _lastGeohash = '';

    // Route selection state
    routeOverviews.clear();
    selectedRouteIndex.value = 0;
    routeChosen.value = false;

    // Nearby drivers state
    showNearbyDrivers.value = false;
    nearbyDrivers.clear();
    nearbyDriverDetails.clear();
    if (stopNearbyDriversSub) {
      stopListeningNearbyDrivers();
    }

    // Trip state
    currentRequestedPaymentMethod = null;
    isCancellingRequest.value = false;
    viewingTrip.value = null;

    // Trip subscriptions
    if (stopCurrentTripSub) {
      currentTripSub?.cancel();
      currentTripSub = null;
      currentRiderTrip.value = null;
    }

    // Only stop driver trip requests subscription if explicitly requested
    // This ensures the subscription remains active unless switching user modes or logging out
    if (stopDriverTripRequestsSub) {
      _driverTripRequestsSub?.cancel();
      _driverTripRequestsSub = null;
      driverTripRequests.clear();
      selectedRequestTripId.value = null;
      _routeDataCache.clear();
    }
  }
  
  // Get route data for a trip with caching
  Future<RouteData?> getCachedRouteData(Trip trip) async {
    // Check embedded data first
    if (trip.routeData != null) {
      return trip.routeData;
    }
    
    // Check cache
    final cacheKey = '${trip.id}_main';
    if (_routeDataCache.containsKey(cacheKey)) {
      return _routeDataCache[cacheKey];
    }
    
    // Prevent multiple simultaneous loads
    if (_routeDataLoading[cacheKey] == true) {
      // Wait a bit and check cache again
      await Future.delayed(const Duration(milliseconds: 100));
      return _routeDataCache[cacheKey];
    }
    
    // Load from separate collection
    _routeDataLoading[cacheKey] = true;
    try {
      final routeData = await trip.getMainRouteData();
      if (routeData != null) {
        _routeDataCache[cacheKey] = routeData;
      }
      return routeData;
    } catch (e) {
      _logger.warning('Failed to load route data for trip ${trip.id}: $e');
      return null;
    } finally {
      _routeDataLoading[cacheKey] = false;
    }
  }
  
  // Get route data synchronously from cache (returns null if not cached)
  RouteData? getCachedRouteDataSync(Trip trip) {
    if (trip.routeData != null) {
      return trip.routeData;
    }
    final cacheKey = '${trip.id}_main';
    return _routeDataCache[cacheKey];
  }

  final isCancellingRequest = false.obs;
  final isChoosingStartPosition = true.obs;
  final isChoosingDestinationPosition = false.obs;

  // Method to set the page controller
  void setPageController(PageController controller) {
    pageController = controller;
  }

  void _enableWakeLock() {
    try {
      _logger.info('Enabling wakelock to prevent screen from turning off during a trip');
      WakelockPlus.enable();
    } catch (e) {
      _logger.severe('Error enabling wakelock: $e');
    }
  }

  void _disableWakeLock() {
    try {
      _logger.info('Disabling wakelock');
      WakelockPlus.disable();
    } catch (e) {
      _logger.severe('Error disabling wakelock: $e');
    }
  }
}
