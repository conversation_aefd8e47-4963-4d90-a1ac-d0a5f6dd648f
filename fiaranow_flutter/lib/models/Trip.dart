import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:fiaranow_flutter/models/ConfigurationModel/TripConfigurationModel.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import '../config/tenant_config.dart';
import '../firebase_cloud_functions.dart' as cloud_functions;
import '../states/AppState.dart';
import 'EventLog.dart';
import 'MobileUser.dart';
import 'RouteDataIds.dart';
import 'TripStatus.dart'; // Import the new TripStatus file

enum InTakeSource { immediate, reservation }

enum ReservationType { scheduled, fullDay }

enum FullDayPriceType { fixed, gasExcluded }

class Trip {
  final String uidPassenger;
  final String? uidChosenDriver;
  final DateTime createdAt;
  final TripStatus status;
  final DateTime? completedAt;
  final DateTime? cancelledAt;
  final String? uidCancelledBy;
  final DocumentReference ref;
  final TripLocation? startLocation;
  final TripLocation? arrivalLocation;
  final TripLocation? driverLocation;
  final double? distanceTotalMeters;
  final RouteData? routeData;
  final RouteData? driverRouteData;
  final RouteData? finalRouteData;
  final String? paymentId; // Reference the Payment document
  final String? customerRequestedPaymentMethod;
  final DateTime? driverStartTime;
  final DateTime? passengerStartTime;
  final DateTime? driverAwaitingTime; // Time when the driver started waiting for the passenger
  final Map<String, dynamic> passenger; // a map that contains the passenger's displayName, photoURL, and phoneNumber
  final Map<String, dynamic>? driver; // a map that contains the driver's displayName, photoURL, and phoneNumber (also lat, lon)
  final bool driverDismissed;
  final bool passengerDismissed;
  final List<String> skippedDriverIds;
  final InTakeSource? inTakeSource;
  final DateTime? clientAlarmTime;
  final DateTime? pickupTime;
  final double? costPrepaid;
  final double? costTotal;
  final double? costDuration;
  final double? costDistance;
  final double? estimatedCost; // Initial estimate shown to user
  final double? realCost; // Actual calculated cost based on logs
  final String? realCostCurrency; // Currency for real cost (default: 'Ar')
  final bool? costCappedAt10Percent; // Flag if cost was capped
  final double? adminOverrideCost; // Manual override by admin
  final String? adminOverrideReason; // Reason for override
  final String? startLocationName;
  final String? arrivalLocationName;
  final TripConfigurationModel tripConfiguration;
  final List<RouteData>? routeOverviews;
  final int? selectedRouteIndex;
  final ReservationType? reservationType;
  final FullDayPriceType? fullDayPriceType;
  final RouteDataIds? routeDataIds;
  final int passengerCount;

  Trip({
    required this.uidPassenger,
    this.uidChosenDriver,
    required this.createdAt,
    required this.status,
    this.completedAt,
    this.cancelledAt,
    this.uidCancelledBy,
    required this.ref,
    this.startLocation,
    this.arrivalLocation,
    this.driverLocation,
    this.distanceTotalMeters,
    this.routeData,
    this.driverRouteData,
    this.finalRouteData,
    this.paymentId,
    this.customerRequestedPaymentMethod,
    this.driverStartTime,
    this.passengerStartTime,
    this.driverAwaitingTime,
    required this.passenger,
    required this.driver,
    this.driverDismissed = false,
    this.passengerDismissed = false,
    this.skippedDriverIds = const [],
    this.inTakeSource,
    this.clientAlarmTime,
    this.pickupTime,
    this.costPrepaid,
    this.costTotal,
    this.costDuration,
    this.costDistance,
    this.estimatedCost,
    this.realCost,
    this.realCostCurrency,
    this.costCappedAt10Percent,
    this.adminOverrideCost,
    this.adminOverrideReason,
    this.startLocationName,
    this.arrivalLocationName,
    required this.tripConfiguration,
    this.routeOverviews,
    this.selectedRouteIndex,
    this.reservationType,
    this.fullDayPriceType,
    this.routeDataIds,
    this.passengerCount = 1,
  });

  String get id => ref.id;

  factory Trip.fromFirestore(DocumentSnapshot<Map<String, dynamic>> doc) {
    final data = doc.data()!;
    final appState = Get.find<AppState>();
    return Trip(
      uidPassenger: data['uidPassenger'],
      uidChosenDriver: data['uidChosenDriver'],
      createdAt: data['createdAt'] != null ? (data['createdAt'] as Timestamp).toDate() : DateTime.now(),
      status: TripStatusExtension.fromString(data['status']),
      completedAt: data['completedAt'] != null ? (data['completedAt'] as Timestamp).toDate() : null,
      cancelledAt: data['cancelledAt'] != null ? (data['cancelledAt'] as Timestamp).toDate() : null,
      uidCancelledBy: data['uidCancelledBy'],
      ref: doc.reference,
      startLocation: data['startLocation'] != null ? TripLocation.fromMap(data['startLocation']) : null,
      arrivalLocation: data['arrivalLocation'] != null ? TripLocation.fromMap(data['arrivalLocation']) : null,
      driverLocation: data['driverLocation'] != null ? TripLocation.fromMap(data['driverLocation']) : null,
      distanceTotalMeters: (data['distanceTotalMeters'] as num?)?.toDouble(),
      routeData: data['routeData'] != null ? RouteData.fromMap(data['routeData']) : null,
      driverRouteData: data['driverRouteData'] != null ? RouteData.fromMap(data['driverRouteData']) : null,
      finalRouteData: data['finalRouteData'] != null ? RouteData.fromMap(data['finalRouteData']) : null,
      paymentId: data['paymentId'],
      customerRequestedPaymentMethod: data['customerRequestedPaymentMethod'],
      driverStartTime: data['driverStartTime'] != null ? (data['driverStartTime'] as Timestamp).toDate() : null,
      passengerStartTime: data['passengerStartTime'] != null ? (data['passengerStartTime'] as Timestamp).toDate() : null,
      driverAwaitingTime: data['driverAwaitingTime'] != null ? (data['driverAwaitingTime'] as Timestamp).toDate() : null,
      passenger: Map<String, dynamic>.from(data['passenger']),
      driver: data['driver'] != null ? Map<String, dynamic>.from(data['driver']) : null,
      driverDismissed: data['driverDismissed'] ?? false,
      passengerDismissed: data['passengerDismissed'] ?? false,
      skippedDriverIds: data['skippedDriverIds'] != null ? List<String>.from(data['skippedDriverIds']) : [],
      inTakeSource: data['inTakeSource'] == 'reservation' ? InTakeSource.reservation : InTakeSource.immediate,
      clientAlarmTime: data['clientAlarmTime'] != null ? (data['clientAlarmTime'] as Timestamp).toDate() : null,
      pickupTime: data['pickupTime'] != null ? (data['pickupTime'] as Timestamp).toDate() : null,
      costPrepaid: (data['costPrepaid'] as num?)?.toDouble(),
      costTotal: (data['costTotal'] as num?)?.toDouble(),
      costDuration: (data['costDuration'] as num?)?.toDouble(),
      costDistance: (data['costDistance'] as num?)?.toDouble(),
      estimatedCost: (data['estimatedCost'] as num?)?.toDouble(),
      realCost: (data['realCost'] as num?)?.toDouble(),
      realCostCurrency: data['realCostCurrency'] as String?,
      costCappedAt10Percent: data['costCappedAt10Percent'] as bool?,
      adminOverrideCost: (data['adminOverrideCost'] as num?)?.toDouble(),
      adminOverrideReason: data['adminOverrideReason'] as String?,
      startLocationName: data['startLocationName'],
      arrivalLocationName: data['arrivalLocationName'],
      routeOverviews:
          data['routeOverviews'] != null ? (data['routeOverviews'] as List).map((r) => RouteData.fromMap(r)).toList() : null,
      selectedRouteIndex: (data['selectedRouteIndex'] as num?)?.toInt(),
      tripConfiguration: data['tripConfiguration'] != null
          ? TripConfigurationModel.fromMap(data['tripConfiguration'])
          : appState.tripConfiguration.value,
      reservationType: data['reservationType'] != null ? ReservationType.values.byName(data['reservationType']) : null,
      fullDayPriceType: data['fullDayPriceType'] != null ? FullDayPriceType.values.byName(data['fullDayPriceType']) : null,
      routeDataIds: data['routeDataIds'] != null ? RouteDataIds.fromMap(data['routeDataIds']) : null,
      passengerCount: (data['passengerCount'] as num?)?.toInt() ?? 1,
    );
  }

  Map<String, dynamic> toFirestore() {
    final map = {
      'uidPassenger': uidPassenger,
      'uidChosenDriver': uidChosenDriver,
      'createdAt': createdAt.isAfter(DateTime.now()) ? FieldValue.serverTimestamp() : createdAt,
      'status': status.name,
      'completedAt': completedAt,
      'cancelledAt': cancelledAt,
      'startLocation': startLocation?.toMap(),
      'arrivalLocation': arrivalLocation?.toMap(),
      'driverLocation': driverLocation?.toMap(),
      'passenger': passenger,
      'occupiedByTripId': null,
      'driverDismissed': driverDismissed,
      'passengerDismissed': passengerDismissed,
      'inTakeSource': inTakeSource?.name,
      'clientAlarmTime': clientAlarmTime,
      'pickupTime': pickupTime,
      'costPrepaid': costPrepaid,
      'costTotal': costTotal,
      'costDuration': costDuration,
      'costDistance': costDistance,
      'paymentId': paymentId,
      // Route data is now stored in separate collection - DO NOT embed
      'startLocationName': startLocationName,
      'arrivalLocationName': arrivalLocationName,
      'tripConfiguration': tripConfiguration.toMap(),
      'customerRequestedPaymentMethod': customerRequestedPaymentMethod,
      'distanceTotalMeters': distanceTotalMeters,
      'reservationType': reservationType?.name,
      'fullDayPriceType': fullDayPriceType?.name,
      'routeDataIds': routeDataIds?.toMap(),
      'passengerCount': passengerCount,
    };

    // Route overviews are now stored in separate collection - DO NOT embed
    // selectedRouteIndex is tracked in routeDataIds.selectedOverviewId

    return map;
  }

  static Future<RouteDataIds> _createRouteDataDocuments({
    required String tripId,
    required RouteData mainRoute,
    List<RouteData>? routeOverviews,
    int? selectedRouteIndex,
  }) async {
    final routeDataColl = FirebaseFirestore.instance
        .collection(TenantConfig.getTenantPath('route_data'));
    
    // Create main route document
    final mainRouteRef = await routeDataColl.add({
      'tripId': tripId,
      'routeType': 'main',
      'routeData': mainRoute.toMap(),
      'createdAt': FieldValue.serverTimestamp(),
    });
    
    // Create overview route documents if available
    List<String>? overviewIds;
    String? selectedOverviewId;
    
    if (routeOverviews != null && routeOverviews.isNotEmpty) {
      overviewIds = [];
      for (int i = 0; i < routeOverviews.length; i++) {
        final ref = await routeDataColl.add({
          'tripId': tripId,
          'routeType': 'overview',
          'routeIndex': i,
          'isSelected': i == selectedRouteIndex,
          'routeData': routeOverviews[i].toMap(),
          'createdAt': FieldValue.serverTimestamp(),
        });
        overviewIds.add(ref.id);
        if (i == selectedRouteIndex) {
          selectedOverviewId = ref.id;
        }
      }
    }
    
    return RouteDataIds(
      main: mainRouteRef.id,
      overviews: overviewIds,
      selectedOverviewId: selectedOverviewId,
    );
  }

  static Future<DocumentReference<Trip>> createTrip({
    required String uidPassenger,
    required TripStatus status,
    required LatLng startPosition,
    required LatLng destinationPosition,
    required MobileUser user,
    required RouteData routeData,
    required TripConfigurationModel tripConfiguration,
    required String customerRequestedPaymentMethod,
    List<RouteData>? routeOverviews,
    int? selectedRouteIndex,
    RouteData? driverRouteData,
    String? startLocationName,
    String? arrivalLocationName,
    int passengerCount = 1,
  }) async {
    final ref = tripsColl.doc();
    final tripId = ref.id;
    
    // Create route data documents FIRST
    final routeDataIds = await _createRouteDataDocuments(
      tripId: tripId,
      mainRoute: routeData,
      routeOverviews: routeOverviews,
      selectedRouteIndex: selectedRouteIndex,
    );
    
    // Create trip with ONLY routeDataIds (no embedded route data)
    await ref.set(Trip(
      uidPassenger: uidPassenger,
      createdAt: DateTime.now().add(const Duration(seconds: 5)),
      status: status,
      ref: ref,
      startLocation: TripLocation(lat: startPosition.latitude, lon: startPosition.longitude),
      arrivalLocation: TripLocation(lat: destinationPosition.latitude, lon: destinationPosition.longitude),
      passenger: {
        'displayName': user.displayName,
        'photoURL': user.photoURL,
        'phoneNumber': user.phoneNumber,
      },
      driver: null,
      // Remove embedded route data - use only routeDataIds
      routeDataIds: routeDataIds,
      startLocationName: startLocationName,
      arrivalLocationName: arrivalLocationName,
      tripConfiguration: tripConfiguration,
      customerRequestedPaymentMethod: customerRequestedPaymentMethod,
      passengerCount: passengerCount,
    ));

    return ref;
  }

  static Future<DocumentReference<Trip>> reserveTrip({
    required String uidPassenger,
    required LatLng startPosition,
    required LatLng destinationPosition,
    required MobileUser user,
    required DateTime reservationTime,
    required RouteData routeData,
    required TripConfigurationModel tripConfiguration,
    required String customerRequestedPaymentMethod,
    List<RouteData>? routeOverviews,
    int? selectedRouteIndex,
    RouteData? driverRouteData,
    String? startLocationName,
    String? arrivalLocationName,
    int passengerCount = 1,
  }) async {
    final ref = tripsColl.doc();
    final tripId = ref.id;
    
    // Create route data documents FIRST
    final routeDataIds = await _createRouteDataDocuments(
      tripId: tripId,
      mainRoute: routeData,
      routeOverviews: routeOverviews,
      selectedRouteIndex: selectedRouteIndex,
    );
    
    // Create trip with ONLY routeDataIds (no embedded route data)
    final trip = Trip(
      uidPassenger: uidPassenger,
      createdAt: DateTime.now(),
      status: TripStatus.reserved,
      ref: ref,
      startLocation: TripLocation(lat: startPosition.latitude, lon: startPosition.longitude),
      arrivalLocation: TripLocation(lat: destinationPosition.latitude, lon: destinationPosition.longitude),
      passenger: {
        'displayName': user.displayName,
        'photoURL': user.photoURL,
        'phoneNumber': user.phoneNumber,
      },
      driver: null,
      inTakeSource: InTakeSource.reservation,
      pickupTime: reservationTime,
      // Remove embedded route data - use only routeDataIds
      routeDataIds: routeDataIds,
      startLocationName: startLocationName?.trim().isNotEmpty == true ? startLocationName!.trim() : null,
      arrivalLocationName: arrivalLocationName?.trim().isNotEmpty == true ? arrivalLocationName!.trim() : null,
      tripConfiguration: tripConfiguration,
      customerRequestedPaymentMethod: customerRequestedPaymentMethod,
      passengerCount: passengerCount,
    );
    await ref.set(trip);
    return ref;
  }

  static Future<DocumentReference<Trip>> reserveFullDayTrip({
    required String uidPassenger,
    required LatLng startPosition,
    required MobileUser user,
    required DateTime reservationTime,
    required FullDayPriceType priceType,
    required TripConfigurationModel tripConfiguration,
    required String customerRequestedPaymentMethod,
    String? startLocationName,
    int passengerCount = 1,
  }) async {
    final ref = tripsColl.doc();
    final trip = Trip(
      uidPassenger: uidPassenger,
      createdAt: DateTime.now(),
      status: TripStatus.reserved,
      ref: ref,
      startLocation: TripLocation(lat: startPosition.latitude, lon: startPosition.longitude),
      passenger: {
        'displayName': user.displayName,
        'photoURL': user.photoURL,
        'phoneNumber': user.phoneNumber,
      },
      driver: null,
      inTakeSource: InTakeSource.reservation,
      pickupTime: reservationTime,
      startLocationName: startLocationName?.trim().isNotEmpty == true ? startLocationName!.trim() : null,
      tripConfiguration: tripConfiguration,
      customerRequestedPaymentMethod: customerRequestedPaymentMethod,
      reservationType: ReservationType.fullDay,
      fullDayPriceType: priceType,
      costPrepaid: priceType == FullDayPriceType.fixed ? 75.0 : 25.0,
      routeDataIds: null, // Will be set by cloud functions for new trips
      passengerCount: passengerCount,
    );
    await ref.set(trip);
    return ref;
  }

  String getTimeUntilPickup() {
    if (pickupTime == null) {
      return 'No pickup time set';
    }

    final now = DateTime.now();
    final difference = pickupTime!.difference(now);

    final hours = difference.inHours;
    final minutes = difference.inMinutes.remainder(60);

    if (difference.isNegative) {
      if (hours == 0) {
        return '${minutes.abs()} minutes ago';
      } else {
        return '${hours.abs()} hours and ${minutes.abs()} minutes ago';
      }
    } else {
      if (hours == 0) {
        return 'In $minutes minutes';
      } else {
        return 'In $hours hours and $minutes minutes';
      }
    }
  }

  Future<void> cancelDriverRequest() async {
    try {
      // Show loading indicator
      Get.dialog(
        const Center(child: CircularProgressIndicator()),
        barrierDismissible: false,
      );

      final result = await cloud_functions.cancelDriverRequest(id);

      // Close loading indicator
      Get.back();

      if (!result) {
        throw Exception('Failed to cancel driver request');
      }
    } catch (e) {
      // Close loading indicator if still showing
      if (Get.isDialogOpen ?? false) Get.back();

      Get.snackbar(
        'Error',
        'Failed to cancel driver request: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
      );
      rethrow;
    }
  }

  Future<void> driverAcceptRequest(LatLng currentLocation) async {
    // Convert Google Maps LatLng to the custom LatLng type used in firebase_cloud_functions
    final customLatLng = cloud_functions.LatLng(lat: currentLocation.latitude, lon: currentLocation.longitude);
    final result = await cloud_functions.driverAcceptRequest(id, customLatLng);

    if (!result) {
      throw Exception('Failed to accept trip request');
    }
  }

  Future<void> driverRejectRequest({
    required String driverUid,
    required TripRejectionReasonType reasonType,
    String? customReason,
  }) async {
    try {
      // Show loading indicator
      Get.dialog(
        const Center(child: CircularProgressIndicator()),
        barrierDismissible: false,
      );

      // Convert enum to string format expected by Firebase Function
      final reasonTypeString = EventLog.convertToScreamingSnakeCase(reasonType.name);

      final result = await cloud_functions.driverRejectRequest(
        id,
        reasonTypeString,
        customReason: customReason,
      );

      // Close loading indicator
      Get.back();

      if (!result) {
        throw Exception('Failed to reject trip request');
      }
    } catch (e) {
      // Close loading indicator if still showing
      if (Get.isDialogOpen ?? false) Get.back();

      Get.snackbar(
        'Error',
        'Failed to reject trip request: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
      );
      rethrow;
    }
  }

  Future<void> recordTripLog(LatLng position) async {
    await ref.collection('logs').add({
      'lat': position.latitude,
      'lon': position.longitude,
      'ts': FieldValue.serverTimestamp(),
      'tsDT': DateTime.now(),
    });
  }

  // Position tracking now handled via logs subcollection
  // Use recordTripLog() method instead

  Future<void> deleteTrip() async {
    // Delete the trip document - logs will be handled by cloud function
    await ref.delete();
  }

  // Get latest position from logs subcollection with fallback to driverLocation
  Stream<TripLocation?> getLatestPositionStream() {
    return FirebaseFirestore.instance
        .collection(TenantConfig.getTenantPath('trips'))
        .doc(id)
        .collection('logs')
        .orderBy('ts', descending: true)
        .limit(1)
        .snapshots()
        .map((snapshot) {
      if (snapshot.docs.isNotEmpty) {
        final log = snapshot.docs.first.data();
        if (log['lat'] != null && log['lon'] != null) {
          return TripLocation(lat: log['lat'], lon: log['lon']);
        }
      }
      // Fallback to driverLocation if no logs available
      return driverLocation;
    });
  }

  bool get isWaitingForMutualStart {
    return status == TripStatus.inProgress && (driverStartTime == null || passengerStartTime == null);
  }

  bool get isWaitingForPassengerStart {
    return status == TripStatus.inProgress && driverStartTime != null && passengerStartTime == null;
  }

  bool get isWaitingForDriverStart {
    return status == TripStatus.inProgress && driverStartTime == null && passengerStartTime != null;
  }

  /// Helper methods for route data with fallback to embedded fields
  Future<RouteData?> getMainRouteData() async {
    if (routeDataIds?.main != null) {
      final doc =
          await FirebaseFirestore.instance.collection(TenantConfig.getTenantPath('route_data')).doc(routeDataIds!.main).get();
      if (doc.exists) {
        final data = doc.data() as Map<String, dynamic>;
        return RouteData.fromMap(data['routeData'] as Map<String, dynamic>);
      }
    }
    return routeData;
  }

  Future<RouteData?> getDriverRouteData() async {
    if (routeDataIds?.driver != null) {
      final doc =
          await FirebaseFirestore.instance.collection(TenantConfig.getTenantPath('route_data')).doc(routeDataIds!.driver).get();
      if (doc.exists) {
        final data = doc.data() as Map<String, dynamic>;
        return RouteData.fromMap(data['routeData'] as Map<String, dynamic>);
      }
    }
    return driverRouteData;
  }

  Future<RouteData?> getFinalRouteData() async {
    if (routeDataIds?.finalRoute != null) {
      final doc = await FirebaseFirestore.instance
          .collection(TenantConfig.getTenantPath('route_data'))
          .doc(routeDataIds!.finalRoute)
          .get();
      if (doc.exists) {
        final data = doc.data() as Map<String, dynamic>;
        return RouteData.fromMap(data['routeData'] as Map<String, dynamic>);
      }
    }
    return finalRouteData;
  }

  Future<List<RouteData>> getRouteOverviews() async {
    if (routeDataIds?.overviews != null && routeDataIds!.overviews!.isNotEmpty) {
      final List<RouteData> routes = [];
      for (final routeId in routeDataIds!.overviews!) {
        final doc = await FirebaseFirestore.instance.collection(TenantConfig.getTenantPath('route_data')).doc(routeId).get();
        if (doc.exists) {
          final data = doc.data() as Map<String, dynamic>;
          routes.add(RouteData.fromMap(data['routeData'] as Map<String, dynamic>));
        }
      }
      return routes;
    }
    return routeOverviews ?? [];
  }

  /// Performs a deep equality check between two lists of trips
  static bool areListsEqual(List<Trip> list1, List<Trip> list2) {
    if (list1.length != list2.length) return false;

    for (int i = 0; i < list1.length; i++) {
      if (!areEqual(list1[i], list2[i])) return false;
    }
    return true;
  }

  /// Performs a deep equality check between two Trip objects
  static bool areEqual(Trip trip1, Trip trip2) {
    return trip1.id == trip2.id &&
        trip1.status == trip2.status &&
        trip1.uidChosenDriver == trip2.uidChosenDriver &&
        trip1.uidPassenger == trip2.uidPassenger &&
        trip1.driverDismissed == trip2.driverDismissed &&
        trip1.passengerDismissed == trip2.passengerDismissed &&
        trip1.costTotal == trip2.costTotal &&
        trip1.costDistance == trip2.costDistance &&
        trip1.costDuration == trip2.costDuration &&
        trip1.costPrepaid == trip2.costPrepaid &&
        trip1.paymentId == trip2.paymentId &&
        trip1.customerRequestedPaymentMethod == trip2.customerRequestedPaymentMethod &&
        _areLocationsEqual(trip1.startLocation, trip2.startLocation) &&
        _areLocationsEqual(trip1.arrivalLocation, trip2.arrivalLocation) &&
        _areLocationsEqual(trip1.driverLocation, trip2.driverLocation) &&
        _areTimestampsEqual(trip1.driverStartTime, trip2.driverStartTime) &&
        _areTimestampsEqual(trip1.passengerStartTime, trip2.passengerStartTime) &&
        _areTimestampsEqual(trip1.driverAwaitingTime, trip2.driverAwaitingTime) &&
        _areTimestampsEqual(trip1.completedAt, trip2.completedAt) &&
        _areTimestampsEqual(trip1.cancelledAt, trip2.cancelledAt) &&
        _areRouteDataEqual(trip1.routeData, trip2.routeData) &&
        _areRouteDataEqual(trip1.driverRouteData, trip2.driverRouteData) &&
        _areRouteDataEqual(trip1.finalRouteData, trip2.finalRouteData) &&
        _areMapEqual(trip1.passenger, trip2.passenger) &&
        _areMapEqual(trip1.driver, trip2.driver) &&
        trip1.distanceTotalMeters == trip2.distanceTotalMeters &&
        _areListsEqual(trip1.routeOverviews, trip2.routeOverviews) &&
        trip1.selectedRouteIndex == trip2.selectedRouteIndex;
  }

  /// Helper method to compare TripLocation objects
  static bool _areLocationsEqual(TripLocation? loc1, TripLocation? loc2) {
    if (loc1 == null && loc2 == null) return true;
    if (loc1 == null || loc2 == null) return false;
    return loc1.lat == loc2.lat && loc1.lon == loc2.lon;
  }

  /// Helper method to compare DateTime objects
  static bool _areTimestampsEqual(DateTime? dt1, DateTime? dt2) {
    if (dt1 == null && dt2 == null) return true;
    if (dt1 == null || dt2 == null) return false;
    return dt1.isAtSameMomentAs(dt2);
  }

  /// Helper method to compare RouteData objects
  static bool _areRouteDataEqual(RouteData? rd1, RouteData? rd2) {
    if (rd1 == null && rd2 == null) return true;
    if (rd1 == null || rd2 == null) return false;
    return rd1.distanceKm == rd2.distanceKm &&
        rd1.durationSec == rd2.durationSec &&
        rd1.polyline == rd2.polyline &&
        rd1.bounds.southwest.latitude == rd2.bounds.southwest.latitude &&
        rd1.bounds.southwest.longitude == rd2.bounds.southwest.longitude &&
        rd1.bounds.northeast.latitude == rd2.bounds.northeast.latitude &&
        rd1.bounds.northeast.longitude == rd2.bounds.northeast.longitude;
  }

  /// Helper method to compare Map objects
  static bool _areMapEqual(Map<String, dynamic>? map1, Map<String, dynamic>? map2) {
    if (map1 == null && map2 == null) return true;
    if (map1 == null || map2 == null) return false;
    if (map1.length != map2.length) return false;

    return map1.entries.every((entry) {
      final key = entry.key;
      final value1 = entry.value;
      final value2 = map2[key];

      if (!map2.containsKey(key)) return false;
      if (value1 == null && value2 == null) return true;
      if (value1 == null || value2 == null) return false;

      // Handle nested maps
      if (value1 is Map && value2 is Map) {
        return _areMapEqual(Map<String, dynamic>.from(value1), Map<String, dynamic>.from(value2));
      }

      return value1 == value2;
    });
  }

  /// Helper method to compare lists of RouteData objects
  static bool _areListsEqual(List<RouteData>? list1, List<RouteData>? list2) {
    if (list1 == null && list2 == null) return true;
    if (list1 == null || list2 == null) return false;
    if (list1.length != list2.length) return false;

    for (int i = 0; i < list1.length; i++) {
      if (!_areRouteDataEqual(list1[i], list2[i])) return false;
    }
    return true;
  }
}

class TripLocation {
  final double lat;
  final double lon;

  TripLocation({required this.lat, required this.lon});

  factory TripLocation.fromMap(Map<String, dynamic> map) {
    return TripLocation(
      lat: (map['lat'] as num).toDouble(),
      lon: (map['lon'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'lat': lat,
      'lon': lon,
    };
  }
}

class RouteData {
  final double distanceKm;
  final int durationSec;
  final String? polyline;
  final LatLngBounds bounds;
  final List<LatLng> mapsPolylines;

  // Add static cache at the class level
  static final Map<int, List<LatLng>> _polylineCache = {};

  RouteData({
    required this.distanceKm,
    required this.durationSec,
    this.polyline,
    required this.bounds,
    required this.mapsPolylines,
  });

  factory RouteData.fromMap(Map<String, dynamic> map) {
    // Create LatLngBounds from the provided bounds data
    LatLngBounds bounds = LatLngBounds(
      southwest: LatLng(
        (map['bounds']['southwest']['lat'] as num).toDouble(),
        (map['bounds']['southwest']['lng'] as num).toDouble(),
      ),
      northeast: LatLng(
        (map['bounds']['northeast']['lat'] as num).toDouble(),
        (map['bounds']['northeast']['lng'] as num).toDouble(),
      ),
    );

    // Handle backward compatibility for field names and type conversion
    double distanceKm;
    int durationSec;

    // Helper function to safely convert to double
    double safeToDouble(dynamic value) {
      if (value == null) return 0.0;
      if (value is num) return value.toDouble();
      if (value is String) return double.tryParse(value) ?? 0.0;
      return 0.0;
    }

    // Helper function to safely convert to int
    int safeToInt(dynamic value) {
      if (value == null) return 0;
      if (value is num) return value.toInt();
      if (value is String) return int.tryParse(value) ?? 0;
      return 0;
    }

    // Check if new field names exist (distanceMeters, durationSeconds)
    if (map.containsKey('distanceMeters') && map.containsKey('durationSeconds')) {
      // New format: convert meters to kilometers
      distanceKm = safeToDouble(map['distanceMeters']) / 1000;
      durationSec = safeToInt(map['durationSeconds']);
    } else {
      // Old format: assume distance is in kilometers, duration in seconds
      distanceKm = safeToDouble(map['distance']);
      durationSec = safeToInt(map['duration']);
    }

    return RouteData(
      distanceKm: distanceKm,
      durationSec: durationSec,
      polyline: map['polyline'] ?? '',
      bounds: bounds,
      mapsPolylines: map['polyline'] != null ? decodePolyline(map['polyline']) : [],
    );
  }

  Map<String, dynamic> toMap() {
    // Convert LatLngBounds back to a Map
    Map<String, dynamic> boundsMap = {
      'southwest': {
        'lat': bounds.southwest.latitude,
        'lng': bounds.southwest.longitude,
      },
      'northeast': {
        'lat': bounds.northeast.latitude,
        'lng': bounds.northeast.longitude,
      },
    };

    return {
      'distanceMeters': (distanceKm * 1000).round(), // Convert km back to meters
      'durationSeconds': durationSec, // Use explicit field name
      'polyline': polyline ?? encodePolyline(mapsPolylines),
      'bounds': boundsMap,
    };
  }

  static List<LatLng> decodePolyline(String encoded) {
    // Use the string's hash code as the cache key
    final int cacheKey = encoded.hashCode;

    // Check cache first
    if (_polylineCache.containsKey(cacheKey)) {
      return List<LatLng>.from(_polylineCache[cacheKey]!);
    }

    List<LatLng> polyline = [];
    int index = 0, len = encoded.length;
    int lat = 0, lng = 0;

    while (index < len) {
      int b, shift = 0, result = 0;
      do {
        b = encoded.codeUnitAt(index++) - 63;
        result |= (b & 0x1F) << shift;
        shift += 5;
      } while (b >= 0x20);
      int dlat = ((result & 1) != 0 ? ~(result >> 1) : (result >> 1));
      lat += dlat;

      shift = 0;
      result = 0;
      do {
        b = encoded.codeUnitAt(index++) - 63;
        result |= (b & 0x1F) << shift;
        shift += 5;
      } while (b >= 0x20);
      int dlng = ((result & 1) != 0 ? ~(result >> 1) : (result >> 1));
      lng += dlng;

      polyline.add(LatLng(lat / 1E5, lng / 1E5));
    }

    // Cache the result before returning
    _polylineCache[cacheKey] = List<LatLng>.from(polyline);

    return polyline;
  }

  static void clearPolylineCache() {
    _polylineCache.clear();
  }

  static String encodePolyline(List<LatLng> points) {
    StringBuffer encoded = StringBuffer();
    int prevLat = 0, prevLng = 0;

    for (final point in points) {
      int lat = (point.latitude * 1E5).round();
      int lng = (point.longitude * 1E5).round();

      int dLat = lat - prevLat;
      int dLng = lng - prevLng;

      prevLat = lat;
      prevLng = lng;

      encoded.write(_encodeValue(dLat));
      encoded.write(_encodeValue(dLng));
    }

    return encoded.toString();
  }

  static String _encodeValue(int value) {
    value = value < 0 ? ~(value << 1) : (value << 1);
    StringBuffer encoded = StringBuffer();

    while (value >= 0x20) {
      encoded.writeCharCode((0x20 | (value & 0x1f)) + 63);
      value >>= 5;
    }
    encoded.writeCharCode(value + 63);

    return encoded.toString();
  }
}

final tripsColl = FirebaseFirestore.instance.collection(TenantConfig.getTenantPath('trips')).withConverter<Trip>(
      fromFirestore: (snapshot, _) => Trip.fromFirestore(snapshot),
      toFirestore: (trip, _) => trip.toFirestore(),
    );
