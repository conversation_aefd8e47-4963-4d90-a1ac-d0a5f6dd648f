import 'package:fiaranow_flutter/l10n/app_localizations.dart';
import 'package:fiaranow_flutter/models/Trip.dart';
import 'package:fiaranow_flutter/models/TripStatus.dart';
import 'package:fiaranow_flutter/services/TripStateService.dart';
import 'package:fiaranow_flutter/states/AppState.dart';
import 'package:fiaranow_flutter/states/NavigationState.dart';
import 'package:fiaranow_flutter/widgets/IconPulsating.dart';
import 'package:fiaranow_flutter/widgets/PriceDisplay.dart';
import 'package:fiaranow_flutter/widgets/TripActionButton.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart';

class DriverTripControl extends StatefulWidget {
  final GoogleMapController? mapController;

  const DriverTripControl({
    super.key,
    required this.mapController,
  });

  @override
  State<DriverTripControl> createState() => _DriverTripControlState();
}

class _DriverTripControlState extends State<DriverTripControl> {
  // Loading state for dismiss button (not using TripActionButton for IconButton)
  bool _isDismissLoading = false;

  Row _createPassengerContactUI(Trip trip) {
    return Row(
      children: [
        CircleAvatar(
          backgroundImage: trip.passenger['photoURL'] != null ? NetworkImage(trip.passenger['photoURL']) : null,
          child: trip.passenger['photoURL'] == null
              ? Text(trip.passenger['displayName'] != null ? trip.passenger['displayName'][0] : 'P')
              : null,
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Flexible(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      trip.passenger['displayName'] ?? 'Passenger',
                      style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                    if (trip.passenger['phoneNumber'] != null && (trip.passenger['phoneNumber'] as String).isNotEmpty)
                      Text(
                        trip.passenger['phoneNumber'],
                        style: const TextStyle(fontSize: 14, color: Colors.grey),
                      ),
                  ],
                ),
              ),
              if (trip.passenger['phoneNumber'] != null && (trip.passenger['phoneNumber'] as String).isNotEmpty)
                IconButton(
                  icon: const Icon(Icons.phone, color: Colors.blue),
                  onPressed: () {
                    FirebaseAnalytics.instance.logEvent(
                      name: 'contact_passenger',
                      parameters: {'method': 'phone', 'trip_id': trip.id, 'widget_name': 'driver_controls'},
                    );
                    launchUrl(Uri(scheme: 'tel', path: trip.passenger['phoneNumber']));
                  },
                ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildLiveCostDisplay(BuildContext context, Trip trip) {
    final bool isFullDayTrip = trip.reservationType == ReservationType.fullDay;

    if (!isFullDayTrip && trip.costTotal == null) {
      return const Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
              SizedBox(width: 8),
              Text("Calculating cost..."),
            ],
          ),
        ],
      );
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.monetization_on),
            const SizedBox(width: 8),
            Flexible(
              child: Text(
                trip.status == TripStatus.completed || trip.status == TripStatus.paid
                    ? AppLocalizations.of(context)!.mapScreen_finalTripCost // Final Trip Cost:
                    : AppLocalizations.of(context)!.mapScreen_currentTripCost, // Current Trip Cost:
                overflow: TextOverflow.visible,
              ),
            ),
            const SizedBox(width: 8),
            if (isFullDayTrip)
              PriceDisplay.bold(
                amount: trip.fullDayPriceType == FullDayPriceType.fixed ? 75 : 25,
                currency: 'EUR',
              )
            else
              PriceDisplay.bold(
                amount: trip.costTotal,
                currency: trip.realCostCurrency ?? 'MGA',
              ),
          ],
        ),

        // For €25 option, show additional gas cost if available
        if (isFullDayTrip &&
            trip.fullDayPriceType == FullDayPriceType.gasExcluded &&
            trip.costDistance != null &&
            trip.costDistance! > 0)
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.local_gas_station),
              const SizedBox(width: 8),
              Text(
                '${AppLocalizations.of(context)!.tripDetails_cost} ',
                style: const TextStyle(fontStyle: FontStyle.italic),
              ),
              PriceDisplay(
                amount: trip.costDistance,
                currency: trip.realCostCurrency ?? 'MGA',
                style: const TextStyle(fontStyle: FontStyle.italic),
              ),
            ],
          ),
        if (trip.distanceTotalMeters != null && trip.distanceTotalMeters! > 0)
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.route),
              const SizedBox(width: 8),
              Text(
                AppLocalizations.of(context)!.mapScreen_roadDistance(
                    (trip.distanceTotalMeters! / 1000).toStringAsFixed(2)), // Road Distance: {distance} km
              ),
            ],
          ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final navigationState = Get.find<NavigationState>();
    final appState = Get.find<AppState>();

    return Obx(() {
      // Check if we're viewing a non-active trip
      if (navigationState.viewingTrip.value != null && navigationState.liveTrip == null) {
        final viewingTrip = navigationState.viewingTrip.value!;
        return _buildViewingTripControl(context, viewingTrip);
      }

      final trip = navigationState.liveTrip;
      if (trip == null) {
        return Center(child: Text(AppLocalizations.of(context)!.mapScreen_noActiveTrip)); // No active trip.
      }

      final bool isFullDayTrip = trip.reservationType == ReservationType.fullDay;

      return Column(
        children: [
          // Header
          Row(
            children: [
              const SizedBox(width: 14),
              if (trip.status == TripStatus.inProgress && trip.driverStartTime != null && trip.passengerStartTime != null)
                const IconPulsating(
                  icon: Icon(Icons.play_arrow, size: 24),
                  color1: Colors.black,
                  color2: Colors.green,
                )
              else if (trip.status == TripStatus.completed || trip.status == TripStatus.paid)
                const Icon(Icons.check_circle, size: 18, color: Colors.green)
              else
                const Icon(Icons.access_time, size: 18),
              const SizedBox(width: 8),
              Text(
                isFullDayTrip
                    ? AppLocalizations.of(context)!.mapScreen_fullDayReservation // "Full Day Reservation"
                    : AppLocalizations.of(context)!.mapScreen_tripControl, // Trip Control
                style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.center_focus_strong),
                    onPressed: () async {
                      // Try embedded route data first, then load from separate collection
                      RouteData? routeData = trip.routeData ?? await trip.getMainRouteData();
                      if (routeData?.bounds != null) {
                        widget.mapController?.animateCamera(
                          CameraUpdate.newLatLngBounds(routeData!.bounds, 50),
                        );
                      }
                    },
                  ),
                  const SizedBox(width: 8),
                  Container(
                    decoration: BoxDecoration(
                      color: navigationState.isFollowingPosition.value ? const Color(0xFF275095) : const Color(0xFFAECCDB),
                    ),
                    child: IconButton(
                      icon: Icon(
                        navigationState.isFollowingPosition.value ? Icons.gps_fixed : Icons.gps_not_fixed,
                        color: Colors.white,
                      ),
                      onPressed: () => navigationState.setFollowingPosition(!navigationState.isFollowingPosition.value),
                    ),
                  ),
                  const SizedBox(width: 8),
                  IconButton(
                    icon: _isDismissLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.close),
                    onPressed: (trip.status == TripStatus.paid || trip.status == TripStatus.cancelled) && !_isDismissLoading
                        ? () async {
                            setState(() => _isDismissLoading = true);
                            try {
                              await TripStateService.instance.dismissTrip(
                                tripId: trip.id,
                                userType: 'driver',
                              );
                              navigationState.clearCurrentRiderTrip();
                              navigationState.reset();
                            } finally {
                              if (mounted) setState(() => _isDismissLoading = false);
                            }
                          }
                        : null,
                  ),
                ],
              ),
            ],
          ),
          const Divider(height: 1),

          // Trip control content - make it scrollable to fix overflow
          Expanded(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    if (trip.status == TripStatus.cancelled) ...[
                      Text(
                        AppLocalizations.of(context)!.mapScreen_sorryTripCancelled, // Sorry, this trip has been cancelled.
                        style: const TextStyle(fontSize: 16, color: Colors.red),
                        textAlign: TextAlign.center,
                      ),
                    ] else if (trip.status == TripStatus.driverApproaching) ...[
                      // Trip information - removed blue background, updated colors
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Pickup time for reserved trips
                          if (trip.pickupTime != null) ...[
                            Row(
                              children: [
                                const Icon(Icons.access_time, size: 20, color: Colors.blue),
                                const SizedBox(width: 8),
                                Text(
                                  '${AppLocalizations.of(context)!.tripDetails_pickupTime}: ',
                                  style: const TextStyle(fontWeight: FontWeight.bold),
                                ),
                                Text(
                                  DateFormat('HH:mm').format(trip.pickupTime!),
                                  style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Colors.blue),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                          ],
                          // Pickup location - blue color
                          Row(
                            children: [
                              const Icon(Icons.location_on, size: 20, color: Colors.blue),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      AppLocalizations.of(context)!.mapScreen_pickupLocation,
                                      style: const TextStyle(fontSize: 12, color: Colors.grey),
                                    ),
                                    Text(
                                      trip.startLocationName ??
                                          AppLocalizations.of(context)!.mapScreen_locationFallback, // "Location"
                                      style: const TextStyle(fontWeight: FontWeight.w500),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          // Destination - green color
                          Row(
                            children: [
                              const Icon(Icons.flag, size: 20, color: Colors.green),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      AppLocalizations.of(context)!.mapScreen_destinationLocation,
                                      style: const TextStyle(fontSize: 12, color: Colors.grey),
                                    ),
                                    Text(
                                      trip.arrivalLocationName ??
                                          AppLocalizations.of(context)!.mapScreen_destinationFallback, // "Destination"
                                      style: const TextStyle(fontWeight: FontWeight.w500),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),
                      Text(
                        AppLocalizations.of(context)!.mapScreen_youShouldNowBeDriving,
                        textAlign: TextAlign.center,
                        style: const TextStyle(fontSize: 14, color: Colors.grey),
                      ), // You should now be driving to approach the client...
                      const SizedBox(height: 20),
                      TripActionButton(
                        label: AppLocalizations.of(context)!.mapScreen_iHaveArrived,
                        tripId: trip.id,
                        transitionType: 'driverArrived',
                        color: Theme.of(context).primaryColor,
                        onPressed: () async {
                          FirebaseAnalytics.instance.logEvent(
                            name: 'trip_accepted',
                            parameters: {'trip_id': trip.id, 'status': trip.status.name, 'widget_name': 'driver_trip_control'},
                          );
                          await TripStateService.instance.driverArrived(tripId: trip.id);
                        },
                      ),
                      const SizedBox(height: 20),
                      _createPassengerContactUI(trip),
                    ] else if (trip.status == TripStatus.driverAwaiting ||
                        (trip.status == TripStatus.inProgress &&
                            (trip.driverStartTime == null || trip.passengerStartTime == null))) ...[
                      Text(
                        // Show specific message based on who has started
                        trip.driverStartTime != null
                            ? AppLocalizations.of(context)!
                                .mapScreen_pleaseWaitForPassenger // Please wait for the passenger to start the trip as well.
                            : AppLocalizations.of(context)!
                                .mapScreen_waitingForBothToStart, // Waiting for both to start the trip...
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 20),
                      // Only show button if driver hasn't started yet
                      if (trip.driverStartTime == null)
                        StartTripButton(
                          tripId: trip.id,
                          userType: 'driver',
                          tripConfiguration: appState.tripConfiguration.value.toMap(),
                          onSuccess: () {
                            navigationState.setFollowingPosition(true);
                          },
                        )
                      else
                        // Show disabled state with checkmark when driver has started
                        Container(
                          height: 48,
                          decoration: BoxDecoration(
                            color: Colors.grey.shade300,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Padding(
                                padding: const EdgeInsets.symmetric(horizontal: 20),
                                child: Row(
                                  children: [
                                    Icon(Icons.check_circle, size: 20, color: Colors.green.shade700),
                                    const SizedBox(width: 8),
                                    Text(
                                      AppLocalizations.of(context)!.tripActionButton_start, // "Démarrer"
                                      style: TextStyle(
                                        fontSize: 13,
                                        fontWeight: FontWeight.w600,
                                        color: Colors.grey.shade700,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      const SizedBox(height: 20),
                      _createPassengerContactUI(trip),
                    ] else if (trip.status == TripStatus.inProgress) ...[
                      _buildLiveCostDisplay(context, trip),
                      const SizedBox(height: 20),
                      CompleteTripButton(
                        tripId: trip.id,
                        // Distance and cost calculated on server
                        onSuccess: () {
                          FirebaseAnalytics.instance.logEvent(
                            name: 'trip_completed',
                            parameters: {
                              'trip_id': trip.id,
                              'final_cost': trip.costTotal ?? 0,
                              'distance': trip.distanceTotalMeters ?? 0,
                              'is_full_day': isFullDayTrip ? 'true' : 'false',
                              'widget_name': 'driver_trip_control'
                            },
                          );
                        },
                      ),
                    ] else if (trip.status == TripStatus.completed) ...[
                      Text(AppLocalizations.of(context)!.mapScreen_tripCompleted), // Trip is completed.
                      const SizedBox(height: 20),
                      _buildLiveCostDisplay(context, trip),
                    ] else if (trip.status == TripStatus.paid) ...[
                      const Icon(
                        Icons.check_circle,
                        size: 48,
                        color: Colors.green,
                      ),
                      const SizedBox(height: 12),
                      Text(AppLocalizations.of(context)!.mapScreen_tripPaid), // Trip is paid.
                      const SizedBox(height: 20),
                      _buildLiveCostDisplay(context, trip),
                    ],
                  ],
                ),
              ),
            ),
          ),
        ],
      );
    });
  }

  // Build control for viewing non-active trips
  Widget _buildViewingTripControl(BuildContext context, Trip trip) {
    final bool isFullDayTrip = trip.reservationType == ReservationType.fullDay;

    return Column(
      children: [
        // Header
        Row(
          children: [
            const SizedBox(width: 14),
            const Icon(Icons.visibility, size: 18),
            const SizedBox(width: 8),
            Text(
              AppLocalizations.of(context)!.mapScreen_tripHeader, // "Trip"
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const Spacer(),
            // Map centering button
            IconButton(
              icon: const Icon(Icons.center_focus_strong),
              onPressed: () async {
                // Try embedded route data first, then load from separate collection
                RouteData? routeData = trip.routeData ?? await trip.getMainRouteData();
                if (routeData?.bounds != null) {
                  widget.mapController?.animateCamera(
                    CameraUpdate.newLatLngBounds(routeData!.bounds, 50),
                  );
                }
              },
            ),
            // Close button
            IconButton(
              icon: const Icon(Icons.close),
              onPressed: () {
                Get.find<NavigationState>().clearViewingTrip();
              },
            ),
          ],
        ),
        Expanded(
          child: SingleChildScrollView(
            child: Container(
              color: Theme.of(context).scaffoldBackgroundColor,
              padding: const EdgeInsets.all(14),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Status
                  Row(
                    children: [
                      const Icon(Icons.circle, size: 12, color: Colors.green),
                      const SizedBox(width: 8),
                      Text(
                        trip.status.getLocalizedName(context),
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Passenger info
                  _createPassengerContactUI(trip),

                  const SizedBox(height: 16),

                  // Locations
                  if (trip.startLocationName != null) ...[
                    Row(
                      children: [
                        const Icon(Icons.location_on, size: 16, color: Colors.blue),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            trip.startLocationName!,
                            style: const TextStyle(fontSize: 14),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                  ],

                  if (trip.arrivalLocationName != null && !isFullDayTrip) ...[
                    Row(
                      children: [
                        const Icon(Icons.location_on, size: 16, color: Colors.green),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            trip.arrivalLocationName!,
                            style: const TextStyle(fontSize: 14),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                  ],

                  // Trip cost
                  if (trip.costTotal != null || isFullDayTrip) ...[
                    Row(
                      children: [
                        const Icon(Icons.monetization_on, size: 16),
                        const SizedBox(width: 8),
                        Text(AppLocalizations.of(context)!.mapScreen_finalTripCost),
                        const SizedBox(width: 8),
                        if (isFullDayTrip)
                          PriceDisplay.bold(
                            amount: trip.fullDayPriceType == FullDayPriceType.fixed ? 75 : 25,
                            currency: 'EUR',
                          )
                        else
                          PriceDisplay.bold(
                            amount: trip.costTotal,
                            currency: trip.realCostCurrency ?? 'MGA',
                          ),
                      ],
                    ),
                    const SizedBox(height: 16),
                  ],

                  // Trip times
                  Row(
                    children: [
                      const Icon(Icons.calendar_today, size: 16),
                      const SizedBox(width: 8),
                      Text(
                        '${AppLocalizations.of(context)!.tripDetails_createdAt}: ${DateFormat('yyyy-MM-dd HH:mm').format(trip.createdAt)}',
                        style: const TextStyle(fontSize: 12, color: Colors.grey),
                      ),
                    ],
                  ),

                  if (trip.completedAt != null) ...[
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        const Icon(Icons.check_circle, size: 16, color: Colors.green),
                        const SizedBox(width: 8),
                        Text(
                          '${AppLocalizations.of(context)!.tripDetails_completedAt}: ${DateFormat('yyyy-MM-dd HH:mm').format(trip.completedAt!)}',
                          style: const TextStyle(fontSize: 12, color: Colors.grey),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
